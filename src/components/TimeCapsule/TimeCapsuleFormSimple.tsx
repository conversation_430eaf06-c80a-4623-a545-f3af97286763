"use client";

import React, { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar as CalendarIcon, Loader2, Upload, File, X, Clock } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format, addDays, isBefore, isAfter, setHours, getHours } from 'date-fns';
import { toast } from 'sonner';

// Define the validation schema for the form
const formSchema = z.object({
  title: z.string().min(2, { message: 'Title must be at least 2 characters' }).max(100),
  message: z.string().max(5000, { message: 'Message must be less than 5000 characters' }).optional(),
  recipient_first_name: z.string().min(1, { message: 'First name is required' }),
  recipient_last_name: z.string().min(1, { message: 'Last name is required' }),
  recipient_email: z.string().email({ message: 'Please enter a valid email address' }),
  delivery_date: z.date({
    required_error: 'Please select a delivery date',
  }).refine(date => isAfter(date, addDays(new Date(), 1)), {
    message: 'Delivery date must be at least 1 day in the future',
  }),
  delivery_hour: z.string().min(1, { message: 'Please select a delivery hour' }),
});

type FormValues = z.infer<typeof formSchema>;

interface TimeCapsuleFormSimpleProps {
  onSuccess: () => void;
}

const TimeCapsuleFormSimple: React.FC<TimeCapsuleFormSimpleProps> = ({ onSuccess }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize the form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      message: '',
      recipient_first_name: '',
      recipient_last_name: '',
      recipient_email: '',
      delivery_date: addDays(new Date(), 7),
      delivery_hour: '12', // Default to noon
    },
  });

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      // Check max files (1)
      if (files.length + newFiles.length > 1) {
        toast.error('Only 1 file can be attached to a time capsule');
        return;
      }

      // Check max file size (250MB)
      const oversizedFiles = newFiles.filter(file => file.size > 250 * 1024 * 1024);
      if (oversizedFiles.length > 0) {
        toast.error('File exceeds the 250MB size limit');
        return;
      }

      setFiles([...files, ...newFiles]);
    }
  };

  // Remove a file from the list
  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  // Upload files to Supabase storage
  const uploadFiles = async (capsuleId: string): Promise<{path: string, name: string, type: string, size: number}[]> => {
    if (files.length === 0) return [];

    setIsUploading(true);
    const fileDetails: {path: string, name: string, type: string, size: number}[] = [];
    const totalFiles = files.length;

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileExt = file.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2)}.${fileExt}`;
        const filePath = `${capsuleId}/${fileName}`;

        // Upload the file
        const formData = new FormData();
        formData.append('file', file);
        formData.append('path', filePath);

        const uploadResponse = await fetch('/api/time-capsules/upload', {
          method: 'POST',
          body: formData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.error || 'Failed to upload file');
        }

        fileDetails.push({
          path: filePath,
          name: file.name,
          type: file.type,
          size: file.size
        });

        // Update progress
        setUploadProgress(Math.round(((i + 1) / totalFiles) * 100));
      }

      return fileDetails;
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Failed to upload files');
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);

    try {
      // Use the API route to create the time capsule
      const response = await fetch('/api/time-capsules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: data.title,
          message: data.message || '',
          recipient_name: `${data.recipient_first_name} ${data.recipient_last_name}`,
          recipient_first_name: data.recipient_first_name,
          recipient_last_name: data.recipient_last_name,
          recipient_email: data.recipient_email,
          delivery_date: setHours(data.delivery_date, parseInt(data.delivery_hour, 10)).toISOString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create time capsule');
      }

      const capsuleData = await response.json();

      // Upload files if any
      if (files.length > 0) {
        try {
          const fileDetails = await uploadFiles(capsuleData.id);

          // Save file metadata
          if (fileDetails.length > 0) {
            const metadataResponse = await fetch('/api/time-capsules/media', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                capsule_id: capsuleData.id,
                files: fileDetails
              }),
            });

            if (!metadataResponse.ok) {
              console.error('Failed to save file metadata');
            }
          }
        } catch (uploadError) {
          console.error('Error uploading files:', uploadError);
          toast.error('Time capsule created but file upload failed');
        }
      }

      toast.success('Time capsule created successfully');

      // Reset form after submission
      form.reset({
        title: '',
        message: '',
        recipient_first_name: '',
        recipient_last_name: '',
        recipient_email: '',
        delivery_date: addDays(new Date(), 7),
        delivery_hour: '12',
      });

      // Clear files
      setFiles([]);

      onSuccess();
    } catch (error: any) {
      console.error('Error creating time capsule:', error);
      toast.error(error.message || 'Failed to create time capsule');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title*</FormLabel>
              <FormControl>
                <Input placeholder="Message for my children" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Message</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Write your message here..."
                  className="h-24"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="recipient_first_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name*</FormLabel>
                <FormControl>
                  <Input placeholder="John" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="recipient_last_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name*</FormLabel>
                <FormControl>
                  <Input placeholder="Doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="recipient_email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Recipient Email*</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="delivery_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Delivery Date*</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {field.value ? (
                          format(field.value, "MMMM d, yyyy")
                        ) : (
                          <span>Select a date</span>
                        )}
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={(date) => {
                        field.onChange(date);
                        // Close the popover after selection
                        document.body.click();
                      }}
                      disabled={(date) => isBefore(date, addDays(new Date(), 1))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  Date of delivery
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="delivery_hour"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Delivery Hour*</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select hour" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Array.from({ length: 24 }, (_, i) => i).map((hour) => (
                      <SelectItem key={hour} value={hour.toString()}>
                        {hour === 0 ? '12 AM (Midnight)' :
                         hour < 12 ? `${hour} AM` :
                         hour === 12 ? '12 PM (Noon)' :
                         `${hour - 12} PM`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Hour of delivery (in 24-hour format)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <FormLabel>Attachment</FormLabel>
              <div className="relative group">
                <span className="cursor-help text-gray-500 bg-gray-100 rounded-full w-5 h-5 inline-flex items-center justify-center text-xs font-medium">i</span>
                <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 invisible group-hover:visible bg-gray-800 text-white text-xs rounded py-1 px-2 w-64">
                  Allowed formats: PDF, DOC, DOCX, JPG, PNG, MP3, MP4 (max 250MB)
                </div>
              </div>
            </div>
            <div className="border border-dashed border-gray-300 rounded-md p-6 text-center">
              <input
                type="file"
                onChange={handleFileChange}
                className="hidden"
                ref={fileInputRef}
              />

              <div className="space-y-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={files.length >= 1 || isSubmitting || isUploading}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Add File
                </Button>

                <p className="text-sm text-gray-500">
                  Add 1 file (max 250MB)
                </p>
              </div>

              {files.length > 0 && (
                <div className="mt-4">
                  <p className="text-sm font-medium mb-2">Selected Files:</p>
                  <ul className="space-y-2">
                    {files.map((file, index) => (
                      <li key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center">
                          <File className="h-4 w-4 mr-2 text-gray-500" />
                          <div className="text-sm">
                            <p className="font-medium truncate max-w-[200px]">{file.name}</p>
                            <p className="text-gray-500 text-xs">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          disabled={isSubmitting || isUploading}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting || isUploading}
          >
            {isSubmitting || isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isUploading ? `Uploading (${uploadProgress}%)` : 'Creating...'}
              </>
            ) : (
              'Create Time Capsule'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default TimeCapsuleFormSimple;
