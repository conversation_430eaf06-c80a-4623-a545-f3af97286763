import React from 'react';
import Link from 'next/link';
import { Search, HelpCircle, Book, FileText, MessageSquare, ArrowRight } from 'lucide-react';
import SimplePageLayout from '@/components/Layout/SimplePageLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

export const metadata = {
  title: 'Help Center | Legalock',
  description: 'Find answers to common questions about using Legalock for your digital legacy planning.',
};

// Sample FAQ categories
const faqCategories = [
  {
    id: 1,
    title: 'Getting Started',
    description: 'Learn the basics of setting up your Legalock account and navigating the platform.',
    icon: <Book className="h-6 w-6" />,
    slug: 'getting-started',
  },
  {
    id: 2,
    title: 'Account & Security',
    description: 'Understand how to manage your account settings and keep your information secure.',
    icon: <HelpCircle className="h-6 w-6" />,
    slug: 'account-security',
  },
  {
    id: 3,
    title: 'Digital Assets',
    description: 'Learn how to document and manage your digital assets effectively.',
    icon: <FileText className="h-6 w-6" />,
    slug: 'digital-assets',
  },
  {
    id: 4,
    title: 'Trustees & Contacts',
    description: 'Understand how to designate trustees and manage emergency contacts.',
    icon: <MessageSquare className="h-6 w-6" />,
    slug: 'trustees-contacts',
  },
];

// Sample popular questions
const popularQuestions = [
  {
    id: 1,
    question: 'How do I add a trustee to my account?',
    slug: 'add-trustee',
  },
  {
    id: 2,
    question: 'What happens to my data after I pass away?',
    slug: 'data-after-passing',
  },
  {
    id: 3,
    question: 'How secure is the Digital Vault?',
    slug: 'vault-security',
  },
  {
    id: 4,
    question: 'Can I change my subscription plan?',
    slug: 'change-subscription',
  },
  {
    id: 5,
    question: 'How do trustees verify my passing?',
    slug: 'trustee-verification',
  },
];

export default function HelpCenterPage() {
  return (
    <SimplePageLayout title="Help Center">
      <div className="mb-12">
        <p className="text-lg text-gray-600 mb-8">
          Find answers to common questions about using Legalock for your digital legacy planning.
        </p>

        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <Input 
            type="text" 
            placeholder="Search for answers..." 
            className="pl-10 py-6 text-lg"
          />
        </div>
      </div>

      <div className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Browse by Category</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {faqCategories.map((category) => (
            <Card key={category.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    {category.icon}
                  </div>
                  <CardTitle>{category.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">{category.description}</CardDescription>
              </CardContent>
              <CardFooter>
                <Button variant="ghost" className="text-primary" asChild>
                  <Link href={`/help/category/${category.slug}`}>
                    View articles
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>

      <div className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Popular Questions</h2>
        <div className="space-y-4">
          {popularQuestions.map((question) => (
            <div key={question.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
              <Link href={`/help/question/${question.slug}`} className="flex items-center justify-between">
                <div className="flex items-center">
                  <HelpCircle className="h-5 w-5 text-primary mr-3" />
                  <span className="text-gray-800">{question.question}</span>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
              </Link>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-gray-100 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Still Need Help?</h2>
        <p className="text-gray-600 mb-6">
          Can't find what you're looking for? Our support team is here to help.
        </p>
        <Button asChild>
          <Link href="/contact">Contact Support</Link>
        </Button>
      </div>
    </SimplePageLayout>
  );
}
