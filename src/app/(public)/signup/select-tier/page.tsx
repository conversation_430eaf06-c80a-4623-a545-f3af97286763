"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Check, ArrowLeft, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { formatPrice, getSubscriptionPlanDetails, SUBSCRIPTION_PLANS } from '@/lib/stripe';
import MainHeader from '@/components/Navigation/MainHeader';
import { toast } from 'sonner';

export default function SelectTierPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const freePlan = getSubscriptionPlanDetails('free');
  const premiumPlan = getSubscriptionPlanDetails(SUBSCRIPTION_PLANS.PREMIUM);

  const handleSelectTier = async (tier: 'free' | 'premium') => {
    setIsLoading(true);
    
    try {
      if (tier === 'free') {
        // For free tier, go directly to registration
        router.push('/register?tier=free');
      } else {
        // For premium tier, go to registration with premium flag
        router.push('/register?tier=premium');
      }
    } catch (error) {
      console.error('Error selecting tier:', error);
      toast.error('Failed to proceed with tier selection');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <MainHeader />
      
      <div className="flex-1 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-4xl">
          {/* Return Button */}
          <div className="mb-6">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Home
              </Link>
            </Button>
          </div>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Choose Your Plan
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Select the plan that best fits your needs. You can upgrade anytime.
            </p>
          </div>

          {/* Pricing Cards */}
          <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {/* Free Plan */}
            <Card className="relative overflow-hidden border-2 border-gray-200 transition-all duration-300 hover:shadow-md">
              <CardHeader className="pb-8">
                <CardTitle className="text-2xl">Essential Legacy</CardTitle>
                <CardDescription>Basic features for everyone</CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold">Free</span>
                  <span className="text-gray-500 ml-2">forever</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {freePlan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => handleSelectTier('free')}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Get Started Free'
                  )}
                </Button>
              </CardFooter>
            </Card>

            {/* Premium Plan */}
            <Card className="relative overflow-hidden border-2 border-primary transition-all duration-300 hover:shadow-md">
              <div className="absolute top-0 right-0 bg-primary text-white px-3 py-1 text-sm font-medium">
                Recommended
              </div>
              <CardHeader className="pb-8">
                <CardTitle className="text-2xl">Legacy Preserver</CardTitle>
                <CardDescription>Advanced features for complete peace of mind</CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold">{formatPrice(premiumPlan.price)}</span>
                  <span className="text-gray-500 ml-2">/ year</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {premiumPlan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  onClick={() => handleSelectTier('premium')}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Start Premium Trial'
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>

          {/* Additional Info */}
          <div className="text-center mt-16 text-gray-600">
            <p className="mb-2">All plans include:</p>
            <ul className="space-y-1">
              <li>Secure data storage</li>
              <li>Regular updates and improvements</li>
              <li>Email support</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
